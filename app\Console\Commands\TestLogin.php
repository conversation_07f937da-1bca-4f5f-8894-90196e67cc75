<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Pengguna;
use Illuminate\Support\Facades\DB;

class TestLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:login {login} {password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test login credentials';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $login = $this->argument('login');
        $password = $this->argument('password');
        
        try {
            // Test database connection
            $this->info('Testing database connection...');
            DB::connection('mysql_aplikasi')->getPdo();
            $this->info('Database connection (mysql_aplikasi): OK');
            
            // Test table access
            $this->info('Testing table access...');
            $user = Pengguna::where('login', $login)->first();
            
            if (!$user) {
                $this->error("User with login '{$login}' not found");
                return 1;
            }

            $this->info("User found: {$user->login}");
            $this->info("User name: " . ($user->nama ?? 'N/A'));
            $this->info("Stored password hash: {$user->password}");
            
            // Test password dengan sistem CodeIgniter
            $private_key = 'KDFLDMSTHBWWSGCBH';
            $inputHashSimple = md5($password);
            $inputHashDouble = md5($private_key . md5($password) . $private_key);

            $this->info("Input password: {$password}");
            $this->info("Simple MD5 hash: {$inputHashSimple}");
            $this->info("Double MD5 hash (CI): {$inputHashDouble}");

            // Cek dengan 3 metode
            if ($user->password === $inputHashDouble) {
                $this->info('Password match: YES (Double MD5 with private key)');
                $this->info('Login test: SUCCESS');
                return 0;
            } elseif ($user->password === $inputHashSimple) {
                $this->info('Password match: YES (Simple MD5)');
                $this->info('Login test: SUCCESS');
                return 0;
            } else {
                $this->error('Password match: NO');
                $this->error('Expected (stored): ' . $user->password);
                $this->error('Got (simple MD5): ' . $inputHashSimple);
                $this->error('Got (double MD5): ' . $inputHashDouble);
                $this->error('Login test: FAILED');
                return 1;
            }
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
