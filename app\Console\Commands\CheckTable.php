<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:table';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check table structure and data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $connection = DB::connection('mysql_aplikasi');
            
            // Show table structure
            $this->info('Table structure for aplikasi.pengguna:');
            $columns = $connection->select('DESCRIBE `aplikasi`.`pengguna`');
            
            $this->table(
                ['Field', 'Type', 'Null', 'Key', 'Default', 'Extra'],
                collect($columns)->map(function ($col) {
                    return [
                        $col->Field,
                        $col->Type,
                        $col->Null,
                        $col->Key ?? '',
                        $col->Default ?? 'NULL',
                        $col->Extra ?? ''
                    ];
                })
            );
            
            // Show sample data
            $this->info("\nSample data (first 3 rows):");
            $data = $connection->select('SELECT * FROM `aplikasi`.`pengguna` LIMIT 3');
            
            if (!empty($data)) {
                $firstRow = (array) $data[0];
                $headers = array_keys($firstRow);
                
                $rows = collect($data)->map(function ($row) {
                    return array_values((array) $row);
                });
                
                $this->table($headers, $rows);
            }
            
            // Look for the specific hash
            $targetHash = 'f3c74c58ba381ac1c7860eef8e49216b';
            $this->info("\nLooking for password hash: {$targetHash}");
            
            $result = $connection->select("SELECT * FROM `aplikasi`.`pengguna` WHERE `password` = ?", [$targetHash]);
            
            if (!empty($result)) {
                $this->info("Found user with target hash:");
                $user = (array) $result[0];
                foreach ($user as $key => $value) {
                    $this->line("{$key}: " . ($value ?? 'NULL'));
                }
            } else {
                $this->error("No user found with target hash");
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
