@extends('layouts.app')

@section('title', 'Data Regkan')

@push('styles')
    <style>
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .table-responsive {
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        /* Specific styling for Data Regkan tables */
        .card-body .table-responsive {
            min-height: 400px;
        }

        #table-pasien-baru_wrapper,
        #table-notifikasi-kanker_wrapper,
        #table-registrasi-kanker_wrapper,
        #table-bukan-kanker_wrapper {
            padding: 0;
        }

        /* Ensure consistent table styling */
        .table th {
            font-weight: 600;
            font-size: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            font-size: 0.875rem;
            vertical-align: middle;
        }

        /* Compact DataTable Pagination Styling */
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 0.25rem 0.5rem !important;
            margin: 0 0.1rem !important;
            font-size: 0.875rem !important;
            min-width: auto !important;
            border-radius: 0.25rem !important;
        }

        /* Comprehensive Active/Current page styling - Multiple selectors for maximum compatibility */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:focus,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:active,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button[aria-current="page"],
        .dataTables_wrapper .dataTables_paginate .paginate_button.active,
        .dataTables_wrapper .dataTables_paginate .paginate_button.selected,
        .dataTables_wrapper .dataTables_paginate span.current,
        .dataTables_wrapper .dataTables_paginate span[aria-current="page"],
        .dataTables_wrapper .dataTables_paginate a.current,
        .dataTables_wrapper .dataTables_paginate a[aria-current="page"],
        .dataTables_wrapper .dataTables_paginate .current,
        .dataTables_wrapper .dataTables_paginate [aria-current="page"] {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border: 1px solid #405189 !important;
            border-color: #405189 !important;
            font-weight: 700 !important;
            box-shadow: 0 2px 4px rgba(64, 81, 137, 0.3) !important;
            text-shadow: none !important;
            outline: 2px solid rgba(64, 81, 137, 0.3) !important;
            outline-offset: 1px !important;
        }

        /* Hover state for non-current buttons */
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current):not([aria-current="page"]):not(.active):not(.selected) {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #405189 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        /* Ensure current page styling overrides hover - Multiple selectors */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,
        .dataTables_wrapper .dataTables_paginate .paginate_button[aria-current="page"]:hover,
        .dataTables_wrapper .dataTables_paginate .paginate_button.active:hover,
        .dataTables_wrapper .dataTables_paginate .paginate_button.selected:hover,
        .dataTables_wrapper .dataTables_paginate span.current:hover,
        .dataTables_wrapper .dataTables_paginate a.current:hover {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            box-shadow: 0 3px 8px rgba(64, 81, 137, 0.4) !important;
            transform: translateY(-1px) !important;
        }

        .dataTables_wrapper .dataTables_info {
            font-size: 0.875rem;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length {
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length select {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .dataTables_wrapper .dataTables_filter input {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Additional compact pagination styling */
        .compact-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.1rem;
        }

        .compact-pagination .paginate_button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 2rem;
            border: 1px solid #dee2e6;
            background-color: #ffffff;
            color: #495057;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
        }

        /* Fallback current page styling with maximum specificity */
        .dataTables_wrapper .compact-pagination .paginate_button.current,
        .dataTables_wrapper .compact-pagination .paginate_button[aria-current="page"] {
            background-color: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            font-weight: 600 !important;
            position: relative;
        }

        /* Add a subtle indicator for current page */
        .dataTables_wrapper .compact-pagination .paginate_button.current::before,
        .dataTables_wrapper .compact-pagination .paginate_button[aria-current="page"]::before,
        .dataTables_wrapper .compact-pagination .paginate_button.force-current-styling::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background-color: #ffffff;
            border-radius: 1px;
        }

        /* Force current styling class - highest priority */
        .dataTables_wrapper .dataTables_paginate .paginate_button.force-current-styling,
        .dataTables_wrapper .compact-pagination .paginate_button.force-current-styling {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border: 1px solid #405189 !important;
            border-color: #405189 !important;
            font-weight: 700 !important;
            box-shadow: 0 2px 4px rgba(64, 81, 137, 0.3) !important;
            outline: 2px solid rgba(64, 81, 137, 0.3) !important;
            outline-offset: 1px !important;
            text-shadow: none !important;
            position: relative !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.force-current-styling:hover,
        .dataTables_wrapper .compact-pagination .paginate_button.force-current-styling:hover {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            box-shadow: 0 3px 8px rgba(64, 81, 137, 0.4) !important;
            transform: translateY(-1px) !important;
        }

        /* Disabled button styling */
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            background: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
            box-shadow: none !important;
        }

        /* Compact pagination for smaller screens */
        @media (max-width: 768px) {
            .dataTables_wrapper .dataTables_paginate .paginate_button {
                padding: 0.2rem 0.4rem !important;
                margin: 0 0.05rem !important;
                font-size: 0.8rem !important;
            }

            /* Mobile current page styling */
            .dataTables_wrapper .dataTables_paginate .paginate_button.current,
            .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
                background-color: #405189 !important;
                color: #ffffff !important;
                border-color: #405189 !important;
                font-weight: 600 !important;
                box-shadow: 0 1px 3px rgba(64, 81, 137, 0.3) !important;
            }

            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter {
                font-size: 0.8rem;
            }
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .card-body .table-responsive {
                min-height: 300px;
            }

            .table th,
            .table td {
                font-size: 0.8rem;
                padding: 0.5rem 0.25rem;
            }
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        {{-- Daftar Pasien Baru --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Baru</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-pasien-baru" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarPasienBaru as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Daftar Pasien Notifikasi Kanker --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-notifikasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarPasienNotifikasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Daftar Pasien Registrasi Kanker --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-registrasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarPasienRegistrasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Daftar Bukan Registrasi Kanker --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-bukan-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarBukanRegistrasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Fungsi untuk menghancurkan DataTable yang sudah ada
            function destroyExistingDataTables() {
                var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];

                tableIds.forEach(function(tableId) {
                    if ($.fn.DataTable.isDataTable(tableId)) {
                        $(tableId).DataTable().destroy();
                        console.log('Destroyed existing DataTable: ' + tableId);
                    }
                });
            }

            // Fungsi untuk inisialisasi DataTables
            function initializeDataTables() {
                try {
                    // Hancurkan DataTable yang sudah ada terlebih dahulu
                    destroyExistingDataTables();

                    var commonOptions = {
                        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        "pageLength": 10,
                        "lengthMenu": [[5, 10, 25, 50], [5, 10, 25, 50]],
                        "pagingType": "simple_numbers", // Changed from full_numbers to simple_numbers for more compact pagination
                        "responsive": true,
                        "autoWidth": false,
                        "destroy": true, // Tambahkan opsi destroy untuk mencegah error reinitialize
                        "language": {
                            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
                            "zeroRecords": "Tidak ada data yang ditemukan",
                            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
                            "infoFiltered": "(difilter dari _MAX_ total entri)",
                            "search": "Cari:",
                            "searchPlaceholder": "Ketik untuk mencari...",
                            "processing": "Sedang memproses...",
                            "paginate": {
                                "first": "Pertama",
                                "last": "Terakhir",
                                "next": "Berikutnya",
                                "previous": "Sebelumnya"
                            },
                            "aria": {
                                "sortAscending": ": aktifkan untuk mengurutkan kolom secara ascending",
                                "sortDescending": ": aktifkan untuk mengurutkan kolom secara descending"
                            }
                        },
                        "drawCallback": function(settings) {
                            var tableId = '#' + settings.sTableId;
                            console.log('DrawCallback executed for table:', tableId);

                            // Remove Bootstrap classes that might interfere
                            $(tableId + '_wrapper .dataTables_paginate .paginate_button').removeClass('page-link');
                            $(tableId + '_wrapper .dataTables_paginate .pagination').removeClass('pagination');

                            // Apply compact styling to pagination
                            $(tableId + '_wrapper .dataTables_paginate').addClass('compact-pagination');

                            // Function to apply current page styling with multiple approaches
                            function applyCurrentPageStyling() {
                                var currentPageSelectors = [
                                    tableId + '_wrapper .dataTables_paginate .paginate_button.current',
                                    tableId + '_wrapper .dataTables_paginate .paginate_button[aria-current="page"]',
                                    tableId + '_wrapper .dataTables_paginate .paginate_button.active',
                                    tableId + '_wrapper .dataTables_paginate .paginate_button.selected',
                                    tableId + '_wrapper .dataTables_paginate span.current',
                                    tableId + '_wrapper .dataTables_paginate a.current'
                                ];

                                var currentPageFound = false;

                                // Try each selector to find the current page
                                currentPageSelectors.forEach(function(selector) {
                                    var elements = $(selector);
                                    if (elements.length > 0) {
                                        console.log('Found current page with selector:', selector, elements.length);
                                        currentPageFound = true;
                                        elements.each(function() {
                                            $(this).css({
                                                'background-color': '#405189 !important',
                                                'background': '#405189',
                                                'color': '#ffffff !important',
                                                'border-color': '#405189 !important',
                                                'border': '1px solid #405189',
                                                'font-weight': '700 !important',
                                                'box-shadow': '0 2px 4px rgba(64, 81, 137, 0.3)',
                                                'outline': '2px solid rgba(64, 81, 137, 0.3)',
                                                'outline-offset': '1px'
                                            }).addClass('force-current-styling');
                                        });
                                    }
                                });

                                // Fallback: Find the current page by checking which page number matches the current page info
                                if (!currentPageFound) {
                                    var pageInfo = $(tableId).DataTable().page.info();
                                    var currentPageNum = pageInfo.page + 1; // DataTables uses 0-based indexing

                                    $(tableId + '_wrapper .dataTables_paginate .paginate_button').each(function() {
                                        var buttonText = $(this).text().trim();
                                        if (buttonText == currentPageNum.toString()) {
                                            console.log('Found current page by number matching:', buttonText);
                                            $(this).css({
                                                'background-color': '#405189 !important',
                                                'background': '#405189',
                                                'color': '#ffffff !important',
                                                'border-color': '#405189 !important',
                                                'border': '1px solid #405189',
                                                'font-weight': '700 !important',
                                                'box-shadow': '0 2px 4px rgba(64, 81, 137, 0.3)',
                                                'outline': '2px solid rgba(64, 81, 137, 0.3)',
                                                'outline-offset': '1px'
                                            }).addClass('force-current-styling');
                                            currentPageFound = true;
                                        }
                                    });
                                }

                                return currentPageFound;
                            }

                            // Apply current page styling
                            var styled = applyCurrentPageStyling();
                            console.log('Current page styling applied:', styled);

                            // Style non-current buttons
                            $(tableId + '_wrapper .dataTables_paginate .paginate_button:not(.force-current-styling):not(.disabled)').each(function() {
                                if (!$(this).hasClass('force-current-styling')) {
                                    $(this).css({
                                        'background-color': '#ffffff',
                                        'background': '#ffffff',
                                        'color': '#495057',
                                        'border-color': '#dee2e6',
                                        'border': '1px solid #dee2e6',
                                        'font-weight': 'normal',
                                        'box-shadow': 'none',
                                        'outline': 'none'
                                    });
                                }
                            });

                            // Hide ellipsis if there are too many pages for mobile
                            if ($(window).width() < 768) {
                                $(tableId + '_wrapper .dataTables_paginate .paginate_button').each(function() {
                                    if ($(this).text() === '…') {
                                        $(this).hide();
                                    }
                                });
                            }

                            // Add click event to maintain styling after page change
                            $(tableId + '_wrapper .dataTables_paginate .paginate_button:not(.disabled)').off('click.customPagination').on('click.customPagination', function() {
                                var clickedButton = $(this);
                                setTimeout(function() {
                                    console.log('Re-applying styling after page change');
                                    applyCurrentPageStyling();
                                }, 100);
                            });
                        }
                    };

                    // Inisialisasi semua tabel dengan opsi yang sama
                    var tables = [
                        '#table-pasien-baru',
                        '#table-notifikasi-kanker',
                        '#table-registrasi-kanker',
                        '#table-bukan-kanker'
                    ];

                    tables.forEach(function(tableId) {
                        try {
                            if ($(tableId).length > 0) {
                                $(tableId).DataTable(commonOptions);
                                console.log('Successfully initialized DataTable: ' + tableId);
                            } else {
                                console.warn('Table not found: ' + tableId);
                            }
                        } catch (error) {
                            console.error('Error initializing DataTable ' + tableId + ':', error);
                        }
                    });

                    // Responsive handling untuk mobile
                    $(window).off('resize.dataregkan').on('resize.dataregkan', function() {
                        $('.dataTables_wrapper').each(function() {
                            try {
                                var table = $(this).find('table').DataTable();
                                if (table) {
                                    table.columns.adjust().responsive.recalc();
                                }
                            } catch (error) {
                                console.warn('Error adjusting table on resize:', error);
                            }
                        });
                    });

                    console.log('All DataTables initialized successfully');

                    // Force apply current page styling after all tables are initialized
                    setTimeout(function() {
                        forceApplyCurrentPageStyling();
                    }, 200);

                } catch (error) {
                    console.error('Error in DataTables initialization:', error);
                }
            }

            // Global function to force apply current page styling to all tables
            function forceApplyCurrentPageStyling() {
                console.log('Force applying current page styling to all tables');

                var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];

                tableIds.forEach(function(tableId) {
                    if ($.fn.DataTable.isDataTable(tableId)) {
                        try {
                            var table = $(tableId).DataTable();
                            var pageInfo = table.page.info();
                            var currentPageNum = pageInfo.page + 1;

                            console.log('Processing table:', tableId, 'Current page:', currentPageNum);

                            // Remove previous force styling
                            $(tableId + '_wrapper .dataTables_paginate .paginate_button').removeClass('force-current-styling');

                            // Find and style the current page button
                            var currentPageFound = false;

                            // Method 1: Try standard selectors
                            var selectors = ['.current', '[aria-current="page"]', '.active', '.selected'];
                            selectors.forEach(function(selector) {
                                var elements = $(tableId + '_wrapper .dataTables_paginate .paginate_button' + selector);
                                if (elements.length > 0) {
                                    elements.addClass('force-current-styling');
                                    currentPageFound = true;
                                    console.log('Found current page with selector:', selector);
                                }
                            });

                            // Method 2: Find by page number if standard selectors failed
                            if (!currentPageFound) {
                                $(tableId + '_wrapper .dataTables_paginate .paginate_button').each(function() {
                                    var buttonText = $(this).text().trim();
                                    if (buttonText == currentPageNum.toString()) {
                                        $(this).addClass('force-current-styling');
                                        currentPageFound = true;
                                        console.log('Found current page by number:', buttonText);
                                    }
                                });
                            }

                            // Method 3: If still not found, style the middle button (fallback)
                            if (!currentPageFound) {
                                var buttons = $(tableId + '_wrapper .dataTables_paginate .paginate_button:not(.previous):not(.next):not(.first):not(.last)');
                                if (buttons.length > 0) {
                                    var middleIndex = Math.floor(buttons.length / 2);
                                    $(buttons[middleIndex]).addClass('force-current-styling');
                                    console.log('Applied fallback styling to middle button');
                                }
                            }

                        } catch (error) {
                            console.error('Error applying styling to table:', tableId, error);
                        }
                    }
                });
            }

            // Tunggu sampai DOM dan semua script selesai dimuat
            setTimeout(function() {
                initializeDataTables();
            }, 100);

            // Additional styling application after page load
            $(window).on('load', function() {
                setTimeout(function() {
                    forceApplyCurrentPageStyling();
                }, 500);
            });

            // Re-apply styling on window resize
            $(window).on('resize', function() {
                setTimeout(function() {
                    forceApplyCurrentPageStyling();
                }, 100);
            });

            // Cleanup saat halaman akan ditinggalkan
            $(window).on('beforeunload', function() {
                destroyExistingDataTables();
            });

            // Debug function - can be called from browser console
            window.debugPagination = function() {
                console.log('=== Pagination Debug Info ===');
                var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];

                tableIds.forEach(function(tableId) {
                    if ($.fn.DataTable.isDataTable(tableId)) {
                        var table = $(tableId).DataTable();
                        var pageInfo = table.page.info();
                        console.log('Table:', tableId);
                        console.log('Current page:', pageInfo.page + 1);
                        console.log('Total pages:', pageInfo.pages);

                        var paginationButtons = $(tableId + '_wrapper .dataTables_paginate .paginate_button');
                        console.log('Pagination buttons found:', paginationButtons.length);

                        paginationButtons.each(function(index) {
                            var classes = $(this).attr('class') || '';
                            var text = $(this).text().trim();
                            var ariaCurrent = $(this).attr('aria-current') || '';
                            console.log('Button', index + ':', text, 'Classes:', classes, 'Aria-current:', ariaCurrent);
                        });
                        console.log('---');
                    }
                });
            };
        });
    </script>
@endpush
