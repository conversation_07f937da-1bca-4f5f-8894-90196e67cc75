# REGKAN - Sistem Registrasi & Konsultasi Online

Aplikasi web berbasis Laravel untuk sistem registrasi dan konsultasi online yang telah dikonversi dari CodeIgniter.

## 🚀 Fitur Utama

- **Sistem Login & Autentikasi** - Login dengan username/password yang aman
- **Dashboard Admin** - Panel kontrol untuk administrator
- **Konsultasi Online** - Sistem konsultasi digital
- **Template Velzon** - UI modern dan responsive
- **Multi-Database Support** - Koneksi ke multiple database

## 🛠️ Teknologi

- **Framework:** Laravel 8.x
- **PHP:** ^7.3|^8.0
- **Database:** MySQL
- **Frontend:** Bootstrap 5, Velzon Template
- **Authentication:** Custom Guard dengan MD5 hashing

## 📋 Persyaratan Sistem

- PHP >= 7.3
- Composer
- MySQL/MariaDB
- Web Server (Apache/Nginx)
- Node.js & NPM (untuk asset compilation)

## ⚡ Instalasi Cepat

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd regkan
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Configuration**
   Edit file `.env` sesuai dengan konfigurasi database Anda:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=your_database
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Jalankan Server**
   ```bash
   php artisan serve
   ```

6. **Akses Aplikasi**
   - URL: `http://localhost:8000`
   - Login: `http://localhost:8000/login`

## 🔐 Kredensial Default

- **Username:** `doksirs`
- **Password:** `eka123`

## 📁 Struktur Project

```
regkan/
├── app/
│   ├── Http/Controllers/
│   │   ├── AuthController.php
│   │   └── DashboardController.php
│   └── Models/
│       └── Pengguna.php
├── resources/views/
│   ├── auth/login.blade.php
│   ├── layouts/
│   └── dashboard.blade.php
├── public/assets/
│   ├── css/
│   ├── js/
│   └── images/
└── routes/web.php
```

## 🔧 Konfigurasi

### Database
Aplikasi menggunakan konfigurasi multi-database. Pastikan koneksi `mysql_aplikasi` sudah dikonfigurasi di `config/database.php`.

### Authentication
Sistem menggunakan custom guard `pengguna` dengan MD5 double hashing untuk kompatibilitas dengan database existing.

## 📝 Development

### Menjalankan Tests
```bash
php artisan test
```

### Asset Compilation
```bash
npm run dev          # Development
npm run production   # Production
```

### Artisan Commands
```bash
php artisan serve              # Start development server
php artisan migrate           # Run migrations
php artisan config:clear      # Clear config cache
```

## 🐛 Troubleshooting

### Asset Tidak Terbaca
Pastikan path asset menggunakan `{{ asset('assets/...') }}` bukan `velzon/assets/`.

### Login Gagal
Periksa konfigurasi database dan pastikan hash password sesuai dengan format MD5 double hashing.

### Permission Error
```bash
chmod -R 775 storage bootstrap/cache
```

## 📚 Dokumentasi Tambahan

- [LAYOUT_CONVERSION_README.md](LAYOUT_CONVERSION_README.md) - Detail konversi layout
- [LOGIN_IMPLEMENTATION.md](LOGIN_IMPLEMENTATION.md) - Implementasi sistem login
- [FINAL_SUCCESS_REPORT.md](FINAL_SUCCESS_REPORT.md) - Laporan implementasi

## 🤝 Kontribusi

1. Fork repository ini
2. Buat branch fitur (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 Lisensi

Project ini menggunakan lisensi [MIT License](https://opensource.org/licenses/MIT).

## 📞 Support

Jika mengalami masalah atau butuh bantuan, silakan buat issue di repository ini.
