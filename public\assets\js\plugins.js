// Dynamic script loading untuk plugin yang dibutuhkan
(function() {
    const hasToastList = document.querySelectorAll("[toast-list]").length > 0;
    const hasChoices = document.querySelectorAll("[data-choices]").length > 0;
    const hasDateProvider = document.querySelectorAll("[data-provider]").length > 0;

    if (hasToastList || hasChoices || hasDateProvider) {
        // Load Toastify dari CDN dengan cara yang lebih aman
        if (hasToastList && !window.Toastify) {
            const toastifyScript = document.createElement('script');
            toastifyScript.src = 'https://cdn.jsdelivr.net/npm/toastify-js';
            toastifyScript.type = 'text/javascript';
            document.head.appendChild(toastifyScript);
        }

        // Load Choices.js jika tersedia
        if (hasChoices) {
            const choicesScript = document.createElement('script');
            choicesScript.src = 'assets/libs/choices.js/public/assets/scripts/choices.min.js';
            choicesScript.type = 'text/javascript';
            choicesScript.onerror = function() {
                console.warn('Choices.js library tidak ditemukan. Silakan install library ini jika diperlukan.');
            };
            document.head.appendChild(choicesScript);
        }

        // Load Flatpickr jika tersedia
        if (hasDateProvider) {
            const flatpickrScript = document.createElement('script');
            flatpickrScript.src = 'assets/libs/flatpickr/flatpickr.min.js';
            flatpickrScript.type = 'text/javascript';
            flatpickrScript.onerror = function() {
                console.warn('Flatpickr library tidak ditemukan. Silakan install library ini jika diperlukan.');
            };
            document.head.appendChild(flatpickrScript);
        }
    }
})();