<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class FindPassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'find:password {hash}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Try to find password for given MD5 hash';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $targetHash = $this->argument('hash');
        
        $this->info("Looking for password that produces MD5 hash: {$targetHash}");
        
        // Common passwords to try
        $passwords = [
            'eka123',
            'admin',
            'password',
            '123456',
            'doksirs',
            'sirs',
            'SIRS',
            'admin123',
            'password123',
            '12345',
            'qwerty',
            'abc123',
            'test',
            'user',
            'guest',
            '2016', // from NIP field
            'doksirs123',
            'sirs123',
            'dokter',
            'doctor',
            'medis',
            'rumahsakit',
            'hospital',
            'sistem',
            'system',
            'aplikasi',
            'app',
            'login',
            'pass',
            'pwd',
            '1234',
            '123',
            'a',
            'b',
            'c',
            '1',
            '2',
            '3',
            'default',
            'temp',
            'temporary',
            'new',
            'old',
            'change',
            'update',
            'reset',
        ];
        
        foreach ($passwords as $password) {
            $hash = md5($password);
            if ($hash === $targetHash) {
                $this->info("✓ FOUND! Password: '{$password}' produces hash: {$hash}");
                return 0;
            }
            $this->line("  {$password} -> {$hash}");
        }
        
        $this->error("Password not found in common list");
        $this->info("You may need to ask the database administrator for the correct password");
        
        return 1;
    }
}
