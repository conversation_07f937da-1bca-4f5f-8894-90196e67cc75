<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DataRegkanController extends Controller
{
    /**
     * Display the data regkan page
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        try {
            // Query untuk daftar pasien baru dari database
            $daftarPasienBaru = DB::select("
                SELECT mp.NORM, CONCAT(IF(mp.GELAR_DEPAN='' OR mp.GELAR_DEPAN IS NULL,'',CONCAT(mp.GELAR_DEPAN,'. ')),UPPER(mp.NAMA),IF(mp.GELAR_BELAKANG='' OR mp.GELAR_BELAKANG IS NULL,'',CONCAT(', ',mp.GELAR_BELAKANG))) NAMA_LENGKAP
                FROM master.pasien mp
                WHERE year(mp.TANGGAL) >= '2023'
                AND mp.`STATUS` !=0 
                ORDER BY mp.NORM ASC
            ");

            // Konversi hasil query ke array untuk konsistensi dengan data lainnya
            $daftarPasienBaru = collect($daftarPasienBaru)->map(function($item) {
                return [
                    'no_mr' => $item->NORM,
                    'nama' => $item->NAMA_LENGKAP
                ];
            })->toArray();

        } catch (\Exception $e) {
            // Jika query database gagal, gunakan data dummy
            Log::error('Database error in DataRegkanController: ' . $e->getMessage());

            $daftarPasienBaru = [
                ['no_mr' => '001', 'nama' => 'Ahmad Suryadi (DB Error)'],
                ['no_mr' => '002', 'nama' => 'Siti Nurhaliza (DB Error)'],
                ['no_mr' => '003', 'nama' => 'Budi Santoso (DB Error)'],
                ['no_mr' => '004', 'nama' => 'Rina Sari (DB Error)'],
                ['no_mr' => '005', 'nama' => 'Joko Widodo (DB Error)'],
            ];
        }

        // Data dummy untuk kategori lainnya
        $daftarPasienNotifikasiKanker = [
            ['no_mr' => '101', 'nama' => 'Maria Dewi'],
            ['no_mr' => '102', 'nama' => 'Joko Widodo'],
            ['no_mr' => '103', 'nama' => 'Rina Sari'],
            ['no_mr' => '104', 'nama' => 'Agus Salim'],
            ['no_mr' => '105', 'nama' => 'Sari Wulandari'],
            ['no_mr' => '106', 'nama' => 'Budi Setiawan'],
            ['no_mr' => '107', 'nama' => 'Lina Marlina'],
            ['no_mr' => '108', 'nama' => 'Eko Prasetyo'],
        ];

        $daftarPasienRegistrasiKanker = [
            ['no_mr' => '201', 'nama' => 'Andi Pratama'],
            ['no_mr' => '202', 'nama' => 'Lestari Wati'],
            ['no_mr' => '203', 'nama' => 'Hendra Gunawan'],
            ['no_mr' => '204', 'nama' => 'Sari Indah'],
            ['no_mr' => '205', 'nama' => 'Budi Hartono'],
            ['no_mr' => '206', 'nama' => 'Nina Sari'],
            ['no_mr' => '207', 'nama' => 'Dedi Kurniawan'],
            ['no_mr' => '208', 'nama' => 'Ratna Dewi'],
            ['no_mr' => '209', 'nama' => 'Ahmad Fauzi'],
            ['no_mr' => '210', 'nama' => 'Siti Aminah'],
        ];

        $daftarBukanRegistrasiKanker = [
            ['no_mr' => '301', 'nama' => 'Dewi Sartika'],
            ['no_mr' => '302', 'nama' => 'Bambang Sutrisno'],
            ['no_mr' => '303', 'nama' => 'Indira Sari'],
            ['no_mr' => '304', 'nama' => 'Joko Susilo'],
            ['no_mr' => '305', 'nama' => 'Maya Sari'],
            ['no_mr' => '306', 'nama' => 'Rudi Hartono'],
        ];

        return view('data-regkan.index', compact(
            'daftarPasienBaru',
            'daftarPasienNotifikasiKanker',
            'daftarPasienRegistrasiKanker',
            'daftarBukanRegistrasiKanker'
        ));
    }

    /**
     * Test method untuk debugging
     */
    public function test()
    {
        try {
            // Query untuk daftar pasien baru dari database
            $daftarPasienBaru = DB::select("
                SELECT mp.NORM as no_mr,
                       CONCAT(
                           IF(mp.GELAR_DEPAN='' OR mp.GELAR_DEPAN IS NULL,'',CONCAT(mp.GELAR_DEPAN,'. ')),
                           UPPER(mp.NAMA),
                           IF(mp.GELAR_BELAKANG='' OR mp.GELAR_BELAKANG IS NULL,'',CONCAT(', ',mp.GELAR_BELAKANG))
                       ) as nama
                FROM master.pasien mp
                WHERE year(mp.TANGGAL) >= '2023'
                AND mp.STATUS != 0
                ORDER BY mp.NORM ASC
                LIMIT 10
            ");
        } catch (\Exception $e) {
            // Jika query database gagal, gunakan data dummy
            $daftarPasienBaru = [
                ['no_mr' => '001', 'nama' => 'Ahmad Suryadi (DB Error)'],
                ['no_mr' => '002', 'nama' => 'Siti Nurhaliza (DB Error)'],
                ['no_mr' => '003', 'nama' => 'Budi Santoso (DB Error)'],
            ];
        }

        // Data dummy untuk kategori lainnya
        $daftarPasienNotifikasiKanker = [
            ['no_mr' => '101', 'nama' => 'Maria Dewi'],
            ['no_mr' => '102', 'nama' => 'Joko Widodo'],
            ['no_mr' => '103', 'nama' => 'Rina Sari'],
            ['no_mr' => '104', 'nama' => 'Agus Salim'],
        ];

        $daftarPasienRegistrasiKanker = [
            ['no_mr' => '201', 'nama' => 'Andi Pratama'],
            ['no_mr' => '202', 'nama' => 'Lestari Wati'],
            ['no_mr' => '203', 'nama' => 'Hendra Gunawan'],
            ['no_mr' => '204', 'nama' => 'Sari Indah'],
            ['no_mr' => '205', 'nama' => 'Budi Hartono'],
        ];

        $daftarBukanRegistrasiKanker = [
            ['no_mr' => '301', 'nama' => 'Dewi Sartika'],
            ['no_mr' => '302', 'nama' => 'Bambang Sutrisno'],
            ['no_mr' => '303', 'nama' => 'Indira Sari'],
        ];

        return view('data-regkan.test', compact(
            'daftarPasienBaru',
            'daftarPasienNotifikasiKanker',
            'daftarPasienRegistrasiKanker',
            'daftarBukanRegistrasiKanker'
        ));
    }
}
