# Implementasi Sistem Login

## Overview
Sistem login telah diimplementasikan menggunakan template Velzon dengan autentikasi ke tabel `aplikasi.pengguna`.

## File yang Dibuat/Dimodifikasi

### 1. Model Pengguna
**File:** `app/Models/Pengguna.php`
- Model untuk tabel `aplikasi.pengguna`
- Menggunakan field `LOGIN` sebagai username (uppercase di database)
- Password menggunakan MD5 hash (field `PASSWORD`)
- Primary key: `ID`
- Field mapping: LOGIN, PASSWORD, NAMA (uppercase di database)

### 2. AuthController
**File:** `app/Http/Controllers/AuthController.php`
- `showLoginForm()` - Menampilkan halaman login
- `login()` - Proses autentikasi dengan Double MD5 + Private Key (sama seperti CodeIgniter)
- `logout()` - Proses logout
- **Password System:** MD5(private_key + MD5(password) + private_key)

### 3. View Login
**File:** `resources/views/auth/login.blade.php`
- Template login berdasarkan `velzon/html/master/auth-signin-cover.html`
- Form login dengan validasi
- Alert untuk success/error messages

### 4. Konfigurasi Auth
**File:** `config/auth.php`
- Guard `pengguna` untuk autentikasi
- Provider `pengguna` menggunakan model Pengguna
- Default guard diubah ke `pengguna`

### 5. Routes
**File:** `routes/web.php`
- Route login (GET/POST) untuk guest
- Route logout (POST) untuk authenticated user
- Protected routes dengan middleware `auth:pengguna`

### 6. Header Layout
**File:** `resources/views/layouts/header.blade.php`
- Menampilkan nama user yang login
- Form logout yang benar dengan CSRF token

### 7. RouteServiceProvider
**File:** `app/Providers/RouteServiceProvider.php`
- HOME constant diubah ke `/dashboard`

## Kredensial Test
- **Username:** doksirs
- **Password:** eka123 ✅
- **Password Hash (Double MD5):** f3c74c58ba381ac1c7860eef8e49216b
- **Private Key:** KDFLDMSTHBWWSGCBH
- **Hash Formula:** MD5(private_key + MD5(password) + private_key)

## Setup Database
File `.env` dikonfigurasi tanpa nama database spesifik (untuk multi-database):
```
DB_CONNECTION=mysql
DB_HOST=***********
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=server5
DB_PASSWORD=simpel
```

### Konfigurasi Database
- **Koneksi:** `mysql_aplikasi` (tanpa database default)
- **Tabel:** `aplikasi.pengguna` (database.schema.table)
- **Model:** Menggunakan koneksi `mysql_aplikasi`

## Testing
### 1. Test Database Connection
```bash
php artisan test:database [database_name]
```

### 2. Test Login Command
```bash
php artisan test:login doksirs eka123
```

### 3. Find Password for Hash
```bash
php artisan find:password f3c74c58ba381ac1c7860eef8e49216b
```

### 4. Manual Test
1. Akses `/login`
2. Masukkan username: `doksirs`
3. Masukkan password: `eka123`
4. Klik "Masuk"

## Flow Autentikasi
1. User mengakses halaman yang dilindungi → redirect ke `/login`
2. User mengisi form login
3. System mencari user berdasarkan `login`
4. System verifikasi password dengan MD5 hash
5. Jika berhasil → login dan redirect ke dashboard
6. Jika gagal → kembali ke login dengan error message

## Middleware
- `guest:pengguna` - Hanya untuk user yang belum login
- `auth:pengguna` - Hanya untuk user yang sudah login

## Session Data
Setelah login berhasil, session akan berisi:
- `ses_nama` - Nama user atau login
- `ses_login` - Username login

## Assets
Template menggunakan assets dari folder `public/assets/`:
- CSS: bootstrap, icons, app, custom
- JS: bootstrap, plugins, password-addon
- Path asset sudah diperbaiki dari `velzon/assets/` ke `assets/`

## Security Features
- CSRF protection pada form
- Password hashing dengan MD5 (sesuai database existing)
- Session-based authentication
- Middleware protection untuk routes

## Next Steps
1. ✅ Test koneksi database: `php artisan test:database`
2. ✅ Test login dengan kredensial: `php artisan test:login doksirs eka123`
3. ✅ Akses `/login` di browser untuk test manual
4. 🎯 **SIAP DIGUNAKAN!** Login dengan username: `doksirs` password: `eka123`
5. Customize tampilan sesuai kebutuhan

## Multi-Database Support
Sistem ini mendukung multiple database karena:
- Koneksi `mysql_aplikasi` tidak memiliki database default
- Nama database ditentukan di level tabel (`aplikasi.pengguna`)
- Dapat dengan mudah menambah model lain untuk database berbeda
