# Menu Data Regkan

## Desk<PERSON>si
Menu "Data Regkan" telah berhasil dibuat untuk menampilkan data tabel dengan 4 kolom sesuai dengan desain yang diminta:

1. **Daftar pasien baru** - Menggunakan query database real
2. **Daftar pasien notifikasi kanker** - Data dummy
3. **Daftar pasien registrasi kanker** - Data dummy
4. **Daftar bukan registrasi kanker** - Data dummy

## ✅ Fitur yang Berhasil Diimplementasi
- **DataTable dengan pagination dan search** ✅
- **Responsive design** ✅
- **Query database untuk pasien baru** ✅
- **Error handling untuk koneksi database** ✅
- **4 tabel terpisah dengan styling sesuai desain** ✅

## File yang Dibuat/Dimodifikasi

### 1. Controller
- **File**: `app/Http/Controllers/DataRegkanController.php`
- **Fungsi**: Menangani logika untuk halaman Data Regkan
- **Method**: `index()` - menampilkan data dummy untuk 4 kategori pasien

### 2. View
- **File**: `resources/views/data-regkan/index.blade.php`
- **Fungsi**: Template untuk menampilkan tabel Data Regkan
- **Fitur**:
  - Tabel dengan 4 kolom utama
  - Sub-header untuk No MR dan Nama di setiap kolom
  - Styling khusus dengan warna sesuai desain
  - Responsive design

### 3. Routes
- **File**: `routes/web.php`
- **Route baru**: `GET /data-regkan` → `DataRegkanController@index`
- **Name**: `data-regkan`
- **Middleware**: `auth:pengguna` (hanya user yang login)

### 4. Sidebar Menu
- **File**: `resources/views/layouts/sidebar.blade.php`
- **Penambahan**: Menu "Data Regkan" dengan icon tabel
- **Active state**: Otomatis aktif saat di halaman Data Regkan

## Struktur Tabel

```
┌─────────────────────┬─────────────────────┬─────────────────────┬─────────────────────┐
│ Daftar pasien baru  │ Daftar pasien       │ Daftar pasien       │ Daftar bukan        │
│                     │ notifikasi kanker   │ registrasi kanker   │ registrasi kanker   │
├─────────────────────┼─────────────────────┼─────────────────────┼─────────────────────┤
│ No MR │ Nama        │ No MR │ Nama        │ No MR │ Nama        │ No MR │ Nama        │
├─────────────────────┼─────────────────────┼─────────────────────┼─────────────────────┤
│ 001   │ Ahmad S.    │ 101   │ Maria D.    │ 201   │ Andi P.     │ 301   │ Dewi S.     │
│ 002   │ Siti N.     │ 102   │ Joko W.     │ 202   │ Lestari W.  │ 302   │ Bambang S.  │
│ ...   │ ...         │ ...   │ ...         │ ...   │ ...         │ ...   │ ...         │
└─────────────────────┴─────────────────────┴─────────────────────┴─────────────────────┘
```

## Warna Desain
- **Header utama**: `#4a7c59` (hijau tua)
- **Sub-header**: `#2c5aa0` (biru tua)
- **Baris genap**: `#f8f9fa` (abu-abu muda)

## Query Database
**Daftar Pasien Baru** menggunakan query:
```sql
SELECT mp.NORM as no_mr,
       CONCAT(
           IF(mp.GELAR_DEPAN='' OR mp.GELAR_DEPAN IS NULL,'',CONCAT(mp.GELAR_DEPAN,'. ')),
           UPPER(mp.NAMA),
           IF(mp.GELAR_BELAKANG='' OR mp.GELAR_BELAKANG IS NULL,'',CONCAT(', ',mp.GELAR_BELAKANG))
       ) as nama
FROM master.pasien mp
WHERE year(mp.TANGGAL) >= '2023'
AND mp.STATUS != 0
ORDER BY mp.NORM ASC
LIMIT 100
```

## DataTable Features
- **Pagination**: 5, 10, 25, 50, atau semua data per halaman
- **Search**: Pencarian real-time di semua kolom
- **Responsive**: Otomatis menyesuaikan ukuran layar
- **Bahasa Indonesia**: Interface dalam bahasa Indonesia
- **CDN**: Menggunakan DataTables via CDN untuk performa optimal

## Cara Mengakses
1. **Production**: Login ke aplikasi → Klik menu "Data Regkan" di sidebar
2. **Testing**: Akses langsung `http://domain.com/test-data-regkan` (tanpa login)

## URL Routes
- **Main**: `/data-regkan` (dengan auth)
- **Test**: `/test-data-regkan` (tanpa auth, untuk testing)

## Pengembangan Selanjutnya
- Implementasi query database untuk 3 kategori lainnya
- Filter berdasarkan tanggal/periode
- Export ke Excel/PDF
- CRUD operations jika diperlukan
- Integrasi dengan sistem notifikasi
