<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:database {database?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test database connection and show available databases';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $database = $this->argument('database');
        
        try {
            // Test connection
            $this->info('Testing database connection...');
            $connection = DB::connection('mysql_aplikasi');
            $pdo = $connection->getPdo();
            $this->info('✓ Database connection: OK');
            
            // Show server info
            $serverVersion = $pdo->getAttribute(\PDO::ATTR_SERVER_VERSION);
            $this->info("✓ MySQL Version: {$serverVersion}");
            
            // Show available databases
            $this->info('Available databases:');
            $databases = $connection->select('SHOW DATABASES');
            foreach ($databases as $db) {
                $dbName = $db->Database;
                $this->line("  - {$dbName}");
            }
            
            // If specific database provided, test access to pengguna table
            if ($database) {
                $this->info("\nTesting access to {$database}.pengguna...");
                try {
                    $result = $connection->select("SELECT COUNT(*) as count FROM `{$database}`.`pengguna`");
                    $count = $result[0]->count;
                    $this->info("✓ Table accessible, found {$count} records");

                    // Show sample data
                    $sample = $connection->select("SELECT login, nama FROM `{$database}`.`pengguna` LIMIT 3");
                    $this->info('Sample users:');
                    foreach ($sample as $user) {
                        $this->line("  - {$user->login}" . ($user->nama ? " ({$user->nama})" : ""));
                    }

                } catch (\Exception $e) {
                    $this->error("✗ Cannot access table: " . $e->getMessage());
                }
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('✗ Database connection failed: ' . $e->getMessage());
            return 1;
        }
    }
}
