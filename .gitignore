# Laravel Framework
/node_modules
/public/hot
/public/storage
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
*.log

# Logs
/storage/logs/*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# IDE & Editor files
/.idea
/.vscode
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Laravel Mix
/public/mix-manifest.json

# Laravel Nova
/nova

# Backup files
*.bak
*.backup
*.tmp

# Compiled assets (uncomment if you want to ignore compiled assets)
# /public/css
# /public/js

# AI Agent System Files
.memory/
.taskmaster/

# Development Documentation Files (temporary/development notes)
ASSET_ERROR_FIX_REPORT.md
ASSET_FIX_NOTES.md
COMPLETE_ERROR_FIX_REPORT.md
DROPDOWN_FIX_GUIDE.md
FINAL_SUCCESS_REPORT.md
HAMBURGER_MENU_FIX.md
LAYOUT_CONVERSION_README.md
LOGIN_IMPLEMENTATION.md

# Velzon Template (if not needed in production)
# /velzon

# Package Lock Files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock

# Local development files
*.local
.vscode/settings.json
.idea/workspace.xml

# Test coverage
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.cache/
