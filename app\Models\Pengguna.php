<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Pengguna extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_aplikasi';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'aplikasi.pengguna';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'ID';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'LOGIN',
        'PASSWORD',
        'NAMA',
        'NIP',
        'NIK',
        'JENIS',
        'STATUS',
        'PASS', // Backup password field
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'PASSWORD',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Get the name of the unique identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return 'LOGIN';
    }

    /**
     * Get the password for the user.
     *
     * @return string
     */
    public function getAuthPassword()
    {
        return $this->PASSWORD;
    }

    /**
     * Accessor for login attribute (lowercase)
     */
    public function getLoginAttribute()
    {
        return $this->attributes['LOGIN'] ?? null;
    }

    /**
     * Accessor for password attribute (lowercase)
     */
    public function getPasswordAttribute()
    {
        return $this->attributes['PASSWORD'] ?? null;
    }

    /**
     * Accessor for nama attribute (lowercase)
     */
    public function getNamaAttribute()
    {
        return $this->attributes['NAMA'] ?? null;
    }
}
