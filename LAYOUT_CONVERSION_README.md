# Konversi Layout dari CodeIgniter ke Laravel

## Ringkasan Perubahan

File-file layout di folder `resources/views/layouts/` telah berhasil dikonversi dari format CodeIgniter ke Laravel Blade template.

## File yang Dikonversi

### 1. **head.blade.php** (sebelumnya head.php)
**Perubahan utama:**
- `<?= base_url() ?>` → `{{ asset() }}`
- Menambahkan `@yield('title', 'default title')`
- <PERSON><PERSON><PERSON><PERSON> `@stack('styles')` untuk CSS tambahan
- Menambahkan `<!DOCTYPE html>` yang sebelumnya hilang

### 2. **header.blade.php** (sebelumnya header.php)
**Perubahan utama:**
- `<?php echo base_url() ?>` → `{{ asset() }}`
- `<?php echo $this->session->userdata('ses_nama') ?>` → `{{ session('ses_nama') ?? 'User' }}`
- `<?php echo base_url('login/logout') ?>` → `{{ route('logout') }}`

### 3. **sidebar.blade.php** (sebelumnya sidebar.php)
**Perubahan utama:**
- `<?php echo base_url() ?>` → `{{ asset() }}`
- `<?php echo $this->session->userdata('NAMA') ?>` → `{{ session('NAMA') ?? 'User' }}`
- `<?php if(isset($menu_active) && $menu_active == 'dashboard') echo 'active'; ?>` → `@if(isset($menu_active) && $menu_active == 'dashboard') active @endif`
- `<?= site_url() ?>` → `{{ route() }}` dengan nama route yang sesuai
- Mengubah semua URL menjadi named routes Laravel:
  - `site_url('admin')` → `route('admin.dashboard')`
  - `site_url('adminKonsulOnline/dataDaftarBaru')` → `route('admin.konsul-online.daftar-baru')`
  - Dan seterusnya...

### 4. **content.blade.php** (sebelumnya content.php)
**Perubahan utama:**
- Menghapus logika PHP CodeIgniter untuk load view
- Menggantinya dengan `@yield('content')` Laravel
- Menambahkan wrapper div dengan class yang sesuai

### 5. **footer.blade.php** (sebelumnya footer.php)
**Perubahan utama:**
- Mengkonversi semua `<?= base_url() ?>` menjadi `{{ asset() }}`
- Menambahkan `@stack('scripts')` untuk JavaScript tambahan
- Mempertahankan semua customizer dan theme settings

### 6. **wrapper.blade.php** (sebelumnya wrapper.php)
**Perubahan utama:**
- `require_once()` → `@include()`
- Mengubah komentar PHP menjadi Blade comment `{{-- --}}`

### 7. **app.blade.php** (FILE BARU)
**File layout utama Laravel yang menggabungkan semua komponen:**
- Struktur HTML lengkap
- Include semua komponen layout
- Support untuk `@yield('content')`, `@stack('styles')`, dan `@stack('scripts')`

## Named Routes yang Perlu Dibuat

Berdasarkan konversi sidebar, Anda perlu membuat route berikut di `routes/web.php`:

```php
// Dashboard
Route::get('/admin', [AdminController::class, 'dashboard'])->name('admin.dashboard');

// Konsultasi Online
Route::prefix('admin/konsul-online')->name('admin.konsul-online.')->group(function () {
    Route::get('/daftar-baru', [AdminKonsulOnlineController::class, 'dataDaftarBaru'])->name('daftar-baru');
    Route::get('/data-all', [AdminKonsulOnlineController::class, 'dataAll'])->name('data-all');
    Route::get('/tertarik', [AdminKonsulOnlineController::class, 'tertarik'])->name('tertarik');
    Route::get('/batal-periksa', [AdminKonsulOnlineController::class, 'batalPeriksa'])->name('batal-periksa');
    Route::get('/fikir-fikir', [AdminKonsulOnlineController::class, 'fikirFikir'])->name('fikir-fikir');
});

// Pendaftaran
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/ippj', [AdminController::class, 'ippj'])->name('ippj');
    Route::get('/admission/list', [AdmissionController::class, 'list'])->name('admission.list');
    Route::get('/deteksi-dini/list', [DeteksiDiniController::class, 'list'])->name('deteksi-dini.list');
    Route::get('/laporan', [LaporanController::class, 'index'])->name('laporan');
});

// Logout
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
```

## Cara Menggunakan Layout Baru

### Menggunakan Layout Utama (app.blade.php)
```blade
@extends('layouts.app')

@section('title', 'Judul Halaman')

@section('content')
    <!-- Konten halaman Anda -->
@endsection

@push('styles')
    <!-- CSS tambahan -->
@endpush

@push('scripts')
    <!-- JavaScript tambahan -->
@endpush
```

### Menggunakan Layout Lama (wrapper.blade.php)
Jika masih ingin menggunakan struktur lama:
```blade
@include('layouts.wrapper')
```

## Session Variables

Pastikan session variables berikut tersedia:
- `ses_nama` - Nama user untuk header
- `NAMA` - Nama user untuk sidebar
- `menu_active` - Untuk menandai menu yang aktif

## Asset Management

Semua asset sekarang menggunakan helper `asset()` Laravel, pastikan:
1. Folder `public/assets/` berisi semua file yang diperlukan
2. Symbolic link sudah dibuat jika menggunakan storage

## Testing

1. Buat controller dan route untuk testing
2. Pastikan semua asset ter-load dengan benar
3. Test semua link di sidebar dan header
4. Verifikasi session data ditampilkan dengan benar

## File Contoh

Lihat `resources/views/dashboard.blade.php` untuk contoh penggunaan layout baru.
