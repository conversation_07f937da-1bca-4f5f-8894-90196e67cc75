localStorage.removeItem("ticket-list");var str_dt=function(e){var e=new Date(e),t=""+["<PERSON>","Feb","<PERSON>","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][e.getMonth()],i=""+e.getDate(),e=e.getFullYear();return t.length<2&&(t="0"+t),[(i=i.length<2?"0"+i:i)+" "+t,e].join(", ")},checkAll=document.getElementById("checkAll"),perPage=(checkAll&&(checkAll.onclick=function(){for(var e=document.querySelectorAll('.form-check-all input[type="checkbox"]'),t=document.querySelectorAll('.form-check-all input[type="checkbox"]:checked').length,i=0;i<e.length;i++)e[i].checked=this.checked,e[i].checked?e[i].closest("tr").classList.add("table-active"):e[i].closest("tr").classList.remove("table-active");document.getElementById("remove-actions").style.display=0<t?"none":"block"}),8),editlist=!1,options={valueNames:["id","tasks_name","client_name","assignedto","create_date","due_date","status","priority"],page:perPage,pagination:!0,plugins:[ListPagination({left:2,right:2})]},ticketsList=new List("ticketsList",options).on("updated",function(e){0==e.matchingItems.length?document.getElementsByClassName("noresult")[0].style.display="block":document.getElementsByClassName("noresult")[0].style.display="none";var t=1==e.i,i=e.i>e.matchingItems.length-e.page;document.querySelector(".pagination-prev.disabled")&&document.querySelector(".pagination-prev.disabled").classList.remove("disabled"),document.querySelector(".pagination-next.disabled")&&document.querySelector(".pagination-next.disabled").classList.remove("disabled"),t&&document.querySelector(".pagination-prev").classList.add("disabled"),i&&document.querySelector(".pagination-next").classList.add("disabled"),e.matchingItems.length<=perPage?document.querySelector(".pagination-wrap").style.display="none":document.querySelector(".pagination-wrap").style.display="flex",e.matchingItems.length==perPage&&document.querySelector(".pagination.listjs-pagination").firstElementChild.children[0].click(),0<e.matchingItems.length?document.getElementsByClassName("noresult")[0].style.display="none":document.getElementsByClassName("noresult")[0].style.display="block"});const xhttp=new XMLHttpRequest;xhttp.onload=function(){var e=JSON.parse(this.responseText);Array.from(e).forEach(function(e){ticketsList.add({id:'<a href="javascript:void(0);" onclick="ViewTickets(this)" data-id="'+e.id+'" class="fw-medium link-primary ticket-id">#VLZ'+e.id+"</a>",tasks_name:e.tasks_name,client_name:e.client_name,assignedto:e.assignedto,create_date:str_dt(e.create_date),due_date:str_dt(e.due_date),priority:isPriority(e.priority),status:isStatus(e.status)}),ticketsList.sort("id",{order:"desc"}),refreshCallbacks()}),ticketsList.remove("id",'<a href="javascript:void(0);" onclick="ViewTickets(this)" data-id="001" class="fw-medium link-primary">#VLZ001</a>')},xhttp.open("GET","assets/json/support-tickets-list.json"),xhttp.send();var isValue=(isCount=(new DOMParser).parseFromString(ticketsList.items.slice(-1)[0]._values.id,"text/html")).body.firstElementChild.innerHTML,idField=document.getElementById("orderId"),tasksTitleField=document.getElementById("tasksTitle-field"),client_nameNameField=document.getElementById("client_nameName-field"),assignedtoNameField=document.getElementById("assignedtoName-field"),dateField=document.getElementById("date-field"),dateDueField=document.getElementById("duedate-field"),priorityField=document.getElementById("priority-field"),statusField=document.getElementById("ticket-status"),addBtn=document.getElementById("add-btn"),editBtn=document.getElementById("edit-btn"),removeBtns=document.getElementsByClassName("remove-item-btn"),editBtns=document.getElementsByClassName("edit-item-btn");function filterOrder(e){var t=e;ticketsList.filter(function(e){e=(matchData=(new DOMParser).parseFromString(e.values().status,"text/html")).body.firstElementChild.innerHTML;return"All"==e||"All"==t||e==t}),ticketsList.update()}function updateList(){var t=document.querySelector("input[name=status]:checked").value;data=userList.filter(function(e){return"All"==t||e.values().sts==t}),userList.update()}refreshCallbacks(),document.getElementById("showModal").addEventListener("show.bs.modal",function(e){e.relatedTarget.classList.contains("edit-item-btn")?(document.getElementById("exampleModalLabel").innerHTML="Edit Ticket",document.getElementById("showModal").querySelector(".modal-footer").style.display="block",document.getElementById("add-btn").innerHTML="Update"):e.relatedTarget.classList.contains("add-btn")?(document.getElementById("modal-id").style.display="none",document.getElementById("exampleModalLabel").innerHTML="Add Ticket",document.getElementById("showModal").querySelector(".modal-footer").style.display="block",document.getElementById("add-btn").innerHTML="Add Ticket"):(document.getElementById("exampleModalLabel").innerHTML="List Ticket",document.getElementById("showModal").querySelector(".modal-footer").style.display="none")}),ischeckboxcheck(),document.getElementById("showModal").addEventListener("hidden.bs.modal",function(){clearFields()}),document.querySelector("#ticketsList").addEventListener("click",function(){ischeckboxcheck()});var table=document.getElementById("ticketTable"),tr=table.getElementsByTagName("tr"),trlist=table.querySelectorAll(".list tr");function SearchData(){var s=document.getElementById("idStatus").value,l=document.getElementById("demo-datepicker").value,n=l.split(" to ")[0],d=l.split(" to ")[1];ticketsList.filter(function(e){var t=(matchData=(new DOMParser).parseFromString(e.values().status,"text/html")).body.firstElementChild.innerHTML,i=!1,a=!1,i="all"==t||"all"==s||t==s,a=new Date(e.values().create_date.slice(0,12))>=new Date(n)&&new Date(e.values().create_date.slice(0,12))<=new Date(d);return i&&a||(i&&""==l?i:a&&""==l?a:void 0)}),ticketsList.update()}var count=14,forms=document.querySelectorAll(".tablelist-form"),example=(Array.prototype.slice.call(forms).forEach(function(i){i.addEventListener("submit",function(e){var t;i.checkValidity()?(e.preventDefault(),""===tasksTitleField.value||""===client_nameNameField.value||""===assignedtoNameField.value||""===dateField.value||""===dateDueField.value||""===statusField.value||""===priorityField.value||editlist?""!==tasksTitleField.value&&""!==client_nameNameField.value&&""!==assignedtoNameField.value&&""!==dateField.value&&""!==dateDueField.value&&""!==statusField.value&&""!==priorityField.value&&editlist&&(t=ticketsList.get({id:idField.value}),Array.from(t).forEach(function(e){(isid=(new DOMParser).parseFromString(e._values.id,"text/html")).body.firstElementChild.innerHTML==itemId&&e.values({id:'<a href="javascript:void(0);" onclick="ViewTickets(this)" data-id="'+idField.value+'" class="fw-medium link-primary">'+idField.value+"</a>",tasks_name:tasksTitleField.value,client_name:client_nameNameField.value,create_date:str_dt(dateField.value),due_date:str_dt(dateDueField.value),priority:isPriority(priorityField.value),status:isStatus(statusField.value)})}),document.getElementById("close-modal").click(),clearFields(),Swal.fire({position:"center",icon:"success",title:"Ticket updated Successfully!",showConfirmButton:!1,timer:2e3,showCloseButton:!0})):(ticketsList.add({id:'<a href="javascript:void(0);" onclick="ViewTickets(this)" data-id="'+count+'" class="fw-medium link-primary ticket-id">#VLZ'+count+"</a>",tasks_name:tasksTitleField.value,client_name:client_nameNameField.value,assignedto:assignedtoNameField.value,create_date:dateField.value,due_date:dateDueField.value,priority:isPriority(priorityField.value),status:isStatus(statusField.value)}),ticketsList.sort("id",{order:"desc"}),document.getElementById("close-modal").click(),clearFields(),refreshCallbacks(),filterOrder("All"),count++,Swal.fire({position:"center",icon:"success",title:"Ticket inserted successfully!",showConfirmButton:!1,timer:2e3,showCloseButton:!0}))):(e.preventDefault(),e.stopPropagation())},!1)}),new Choices(priorityField,{searchEnabled:!1})),statusVal=new Choices(statusField,{searchEnabled:!1});function isStatus(e){switch(e){case"Open":return'<span class="badge bg-success-subtle text-success text-uppercase">'+e+"</span>";case"Inprogress":return'<span class="badge bg-warning-subtle text-warning text-uppercase">'+e+"</span>";case"Closed":return'<span class="badge bg-danger-subtle text-danger text-uppercase">'+e+"</span>";case"New":return'<span class="badge bg-info-subtle text-info text-uppercase">'+e+"</span>"}}function isPriority(e){switch(e){case"High":return'<span class="badge bg-danger text-uppercase">'+e+"</span>";case"Low":return'<span class="badge bg-success text-uppercase">'+e+"</span>";case"Medium":return'<span class="badge bg-warning text-uppercase">'+e+"</span>"}}function ischeckboxcheck(){Array.from(document.getElementsByName("checkAll")).forEach(function(i){i.addEventListener("change",function(e){1==i.checked?e.target.closest("tr").classList.add("table-active"):e.target.closest("tr").classList.remove("table-active");var t=document.querySelectorAll('[name="checkAll"]:checked').length;e.target.closest("tr").classList.contains("table-active"),document.getElementById("remove-actions").style.display=0<t?"block":"none"})})}function refreshCallbacks(){removeBtns&&Array.from(removeBtns).forEach(function(e){e.addEventListener("click",function(e){e.target.closest("tr").children[1].innerText,itemId=e.target.closest("tr").children[1].innerText;e=ticketsList.get({id:itemId});Array.from(e).forEach(function(e){var t=(deleteid=(new DOMParser).parseFromString(e._values.id,"text/html")).body.firstElementChild;deleteid.body.firstElementChild.innerHTML==itemId&&document.getElementById("delete-record").addEventListener("click",function(){ticketsList.remove("id",t.outerHTML),document.getElementById("deleteRecord-close").click()})})})}),editBtns&&Array.from(editBtns).forEach(function(e){e.addEventListener("click",function(e){e.target.closest("tr").children[1].innerText,itemId=e.target.closest("tr").children[1].innerText;e=ticketsList.get({id:itemId});Array.from(e).forEach(function(e){var t=(isid=(new DOMParser).parseFromString(e._values.id,"text/html")).body.firstElementChild.innerHTML;t==itemId&&(editlist=!0,idField.value=t,tasksTitleField.value=e._values.tasks_name,client_nameNameField.value=e._values.client_name,assignedtoNameField.value=e._values.assignedto,dateField.value=e._values.create_date,dateDueField.value=e._values.due_date,example&&example.destroy(),example=new Choices(priorityField,{searchEnabled:!1}),t=(val=(new DOMParser).parseFromString(e._values.priority,"text/html")).body.firstElementChild.innerHTML,example.setChoiceByValue(t),statusVal&&statusVal.destroy(),statusVal=new Choices(statusField,{searchEnabled:!1}),t=(val=(new DOMParser).parseFromString(e._values.status,"text/html")).body.firstElementChild.innerHTML,statusVal.setChoiceByValue(t),flatpickr("#date-field",{dateFormat:"d M, Y",defaultDate:e._values.create_date}),flatpickr("#duedate-field",{dateFormat:"d M, Y",defaultDate:e._values.due_date}))})})})}function clearFields(){tasksTitleField.value="",client_nameNameField.value="",assignedtoNameField.value="",dateField.value="",dateDueField.value="",example&&example.destroy(),example=new Choices(priorityField),statusVal&&statusVal.destroy(),statusVal=new Choices(statusField)}function deleteMultiple(){ids_array=[];var e,t=document.querySelectorAll(".form-check [value=option1]");for(i=0;i<t.length;i++)1==t[i].checked&&(e=t[i].parentNode.parentNode.parentNode.querySelector("td [data-id]").getAttribute("data-id"),ids_array.push(e));"undefined"!=typeof ids_array&&0<ids_array.length?Swal.fire({title:"Are you sure?",text:"You won't be able to revert this!",icon:"warning",showCancelButton:!0,customClass:{confirmButton:"btn btn-primary w-xs me-2 mt-2",cancelButton:"btn btn-danger w-xs mt-2"},confirmButtonText:"Yes, delete it!",buttonsStyling:!1,showCloseButton:!0}).then(function(e){if(e.value){for(i=0;i<ids_array.length;i++)ticketsList.remove("id",`<a href="javascript:void(0);" onclick="ViewTickets(this)" data-id="${ids_array[i]}" class="fw-medium link-primary ticket-id">#VLZ${ids_array[i]}</a>`);document.getElementById("remove-actions").style.display="none",document.getElementById("checkAll").checked=!1,Swal.fire({title:"Deleted!",text:"Your data has been deleted.",icon:"success",customClass:{confirmButton:"btn btn-info w-xs mt-2"},buttonsStyling:!1})}}):Swal.fire({title:"Please select at least one checkbox",customClass:{confirmButton:"btn btn-info"},buttonsStyling:!1,showCloseButton:!0})}function ViewTickets(e){var e=e.getAttribute("data-id"),e=ticketsList.get("id",'<a href="javascript:void(0);" onclick="ViewTickets(this)" data-id="'+e+'" class="fw-medium link-primary ticket-id">#VLZ'+e+"</a>"),t=e[0]._values.id,i=document.createElement("div"),t=(i.innerHTML=t,i.innerText.slice(4));localStorage.setItem("ticket-list",JSON.stringify(e[0]._values)),localStorage.setItem("option","view-ticket"),localStorage.setItem("ticket_no",t),window.location.assign("apps-tickets-details.html")}document.querySelector(".pagination-next").addEventListener("click",function(){document.querySelector(".pagination.listjs-pagination")&&document.querySelector(".pagination.listjs-pagination").querySelector(".active")&&document.querySelector(".pagination.listjs-pagination").querySelector(".active").nextElementSibling.children[0].click()}),document.querySelector(".pagination-prev").addEventListener("click",function(){document.querySelector(".pagination.listjs-pagination")&&document.querySelector(".pagination.listjs-pagination").querySelector(".active")&&document.querySelector(".pagination.listjs-pagination").querySelector(".active").previousSibling.children[0].click()});