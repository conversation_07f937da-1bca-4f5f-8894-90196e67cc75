<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Pengguna;
use Illuminate\Support\Facades\DB;

class FindUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'find:user {search?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find users in pengguna table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $search = $this->argument('search');
        
        try {
            if ($search) {
                $this->info("Searching for users containing: {$search}");
                $users = Pengguna::where('login', 'LIKE', "%{$search}%")
                    ->orWhere('nama', 'LIKE', "%{$search}%")
                    ->limit(10)
                    ->get();
            } else {
                $this->info("Showing first 10 users:");
                $users = Pengguna::limit(10)->get();
            }
            
            if ($users->isEmpty()) {
                $this->error('No users found');
                return 1;
            }
            
            $this->info('Found users:');
            $this->table(
                ['Login', 'Nama', 'Password Hash'],
                $users->map(function ($user) {
                    return [
                        $user->login ?? 'NULL',
                        $user->nama ?? 'NULL',
                        substr($user->password ?? 'NULL', 0, 20) . '...'
                    ];
                })
            );
            
            // Check for specific hash
            $targetHash = 'f3c74c58ba381ac1c7860eef8e49216b';
            $this->info("\nLooking for password hash: {$targetHash}");
            
            $userWithHash = Pengguna::where('password', $targetHash)->first();
            if ($userWithHash) {
                $this->info("Found user with target hash:");
                $this->line("Login: " . ($userWithHash->login ?? 'NULL'));
                $this->line("Nama: " . ($userWithHash->nama ?? 'NULL'));
            } else {
                $this->error("No user found with target hash");
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
