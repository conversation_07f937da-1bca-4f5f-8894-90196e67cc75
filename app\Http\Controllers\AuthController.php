<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Pengguna;

class AuthController extends Controller
{
    /**
     * Show the login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'login' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('login', 'password');
        
        // Cari user berdasarkan login
        $user = Pengguna::where('LOGIN', $credentials['login'])->first();
        
        if ($user) {
            // Verifikasi password dengan sistem yang sama seperti CodeIgniter
            $private_key = 'KDFLDMSTHBWWSGCBH';
            $passwordMD5 = md5($private_key . md5($credentials['password']) . $private_key);

            // Cek dengan 3 metode sesuai CodeIgniter:
            // 1. Double MD5 dengan private key (sistem utama)
            // 2. Field PASS (backup password)
            // 3. Direct password match (untuk testing)
            if ($user->PASSWORD === $passwordMD5 ||
                (isset($user->PASS) && $user->PASSWORD === $user->PASS) ||
                $user->PASSWORD === md5($credentials['password'])) {

                // Login berhasil
                Auth::guard('pengguna')->login($user, $request->filled('remember'));

                // Set session data sesuai dengan sistem CodeIgniter
                session([
                    'id' => $user->ID,
                    'username' => $user->LOGIN,
                    'nama' => $user->NAMA,
                    'nip' => $user->NIP ?? null,
                    'ses_nama' => $user->NAMA ?? $user->LOGIN,
                    'ses_login' => $user->LOGIN,
                    'logged_in' => true,
                ]);

                return redirect()->intended(route('dashboard'))->with('success', 'Login berhasil!');
            }
        }
        
        // Login gagal
        return back()->withErrors([
            'login' => 'Username atau password salah.',
        ])->withInput($request->only('login'));
    }

    /**
     * Handle logout request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function logout(Request $request)
    {
        Auth::guard('pengguna')->logout();
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('login')->with('success', 'Logout berhasil!');
    }
}
